"""
此文件定义了用于记录图像推理次数统计的 PostgreSQL 数据库表结构。

该表专门用于统计和跟踪每个图像文件的推理次数，支持高效的查询和统计操作。
表结构以 Python 字典的形式定义，包含了列、约束和索引等信息，
可以被用于动态生成 SQL CREATE TABLE 语句或被 ORM (对象关系映射) 使用。
"""

# 存储图像推理次数统计数据
INFERENCE_STATISTICS_TABLE = {
    "table_comment": "存储图像推理次数统计数据，用于跟踪每个图像文件的推理频次",
    "columns": {
        "stat_id": {
            "type": "SERIAL",
            "primary_key": True,
            "comment": "统计记录的唯一标识符，自增主键"
        },
        "image_id": {
            "type": "VARCHAR(500)",
            "nullable": False,
            "comment": "图像的唯一标识符"
        },
        "image_path": {
            "type": "VARCHAR(500)",
            "nullable": False,
            "comment": "图像的完整路径"
        },
        "inference_count": {
            "type": "INTEGER",
            "nullable": False,
            "default": 0,
            "comment": "该图像文件的累计推理次数"
        },
        "model_md5": {
            "type": "VARCHAR(32)",
            "nullable": True,
            "comment": "用于推理的模型文件的MD5哈希值"
        },
        "created_at": {
            "type": "TIMESTAMP WITH TIME ZONE",
            "default": "CURRENT_TIMESTAMP",
            "nullable": False,
            "comment": "统计记录创建时间"
        },
        "updated_at": {
            "type": "TIMESTAMP WITH TIME ZONE",
            "default": "CURRENT_TIMESTAMP",
            "nullable": False,
            "comment": "统计记录最后更新时间"
        }
    },
    "constraints": [
        {
            "type": "UNIQUE",
            "columns": ["image_path"],
            "name": "uk_inference_image_path",
            "comment": "确保每个图像路径在表中唯一，防止重复统计"
        },
        {
            "type": "CHECK",
            "condition": "inference_count >= 0",
            "name": "chk_inference_count_positive",
            "comment": "确保推理次数不能为负数"
        }
    ],
    # 索引 (Indexes) 是数据库中用于加速查询速度的特殊数据结构。
    # 针对推理统计表的核心查询场景设计的索引：
    "indexes": [
        {
            "columns": ["image_id"],
            "name": "idx_inference_image_id",
            "method": "BTREE",
            "comment": "加速根据图像唯一标识符查找统计信息的查询"
        },
        {
            "columns": ["inference_count"],
            "name": "idx_inference_count",
            "method": "BTREE",
            "comment": "加速根据推理次数进行排序和筛选的查询"
        },
        {
            "columns": ["created_at"],
            "name": "idx_inference_created_at",
            "method": "BTREE",
            "comment": "加速根据创建时间进行时间范围查询"
        },
        {
            "columns": ["model_md5"],
            "name": "idx_inference_model_md5",
            "method": "BTREE",
            "comment": "加速根据模型MD5哈希值查询统计信息"
        }
    ]
}


# 使用示例和说明
"""
使用示例：

1. 导入schema定义：
   from inference_schema import INFERENCE_STATISTICS_TABLE

2. 获取表结构：
   schema = INFERENCE_STATISTICS_TABLE

3. 获取列定义：
   columns = INFERENCE_STATISTICS_TABLE["columns"]

4. 常见查询场景：
   - 查询推理次数最多的图像：ORDER BY inference_count DESC
   - 查询最近创建的统计记录：ORDER BY created_at DESC
   - 查询特定图像的推理次数：WHERE image_path = '/path/to/image.jpg'
   - 查询特定模型的推理统计：WHERE model_md5 = 'abc123...'

注意事项：
- inference_count 字段会随着每次推理自动递增
- updated_at 字段需要在推理时更新
"""
