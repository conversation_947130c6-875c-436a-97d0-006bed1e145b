# 图像推理次数统计管理器使用文档

## 概述

`InferenceStatisticsManager` 是一个专门用于管理图像推理次数统计的类，提供推理次数记录、限制控制、统计查询等功能。该类与现有的YOLO推理流程无缝集成，支持PostgreSQL数据库存储和高效的批量处理。

## 核心功能

### 1. 推理次数统计
- 自动记录每个图像的推理次数
- 支持模型MD5跟踪，区分不同模型的推理
- 提供详细的时间戳记录

### 2. 推理限制控制
- 可配置的推理次数限制
- 支持启用/禁用限制功能
- 批量图像的高效限制检查

### 3. 统计信息查询
- 推理统计摘要报告
- 推理次数排行榜
- 模型使用统计

### 4. 数据库集成
- 基于PostgreSQL的持久化存储
- 自动表结构创建和管理
- 高效的查询和更新操作

## 快速开始

### 1. 基本初始化

```python
from anylabeling.customize_ui.src.ui_operate.predict_image.inference_statistics_manager import InferenceStatisticsManager

# 创建管理器实例
manager = InferenceStatisticsManager(
    line_edit_manager=line_edit_manager,  # UI控件管理器
    logger=logger,                        # 日志记录器
    log_output=log_output                 # UI日志输出器
)
```

### 2. 配置推理限制

```python
# 启用推理限制，设置默认限制为5次
manager.set_inference_limit_config(enabled=True, default_limit=5)

# 禁用推理限制
manager.set_inference_limit_config(enabled=False)
```

### 3. 推理前检查

```python
image_path = "/path/to/image.jpg"

# 检查单个图像
if manager.check_inference_limit(image_path):
    print("可以进行推理")
    # 执行推理...
    # 更新推理次数
    manager.update_inference_count(image_path)
else:
    print("已达推理次数限制")

# 批量检查
image_list = ["/path/to/img1.jpg", "/path/to/img2.jpg", "/path/to/img3.jpg"]
results = manager.batch_check_inference_limits(image_list)
allowed_images = [path for path, allowed in results.items() if allowed]
```

### 4. 统计信息查询

```python
# 获取推理次数
count = manager.get_inference_count("/path/to/image.jpg")
print(f"推理次数: {count}")

# 获取统计摘要
summary = manager.get_statistics_summary()
if summary:
    print(f"总图像数: {summary['total_images']}")
    print(f"总推理次数: {summary['total_inferences']}")
    print(f"平均推理次数: {summary['avg_inferences_per_image']:.2f}")

# 获取推理次数最多的图像
top_images = manager.get_top_inference_images(10)
for img in top_images:
    print(f"{img['image_path']}: {img['inference_count']}次")
```

## UI控件配置

### 必需的UI控件

| 控件名称 | 用途 | 示例值 |
|---------|------|--------|
| lineEdit_174 | 数据库名称 | "inference_db" |
| lineEdit_63 | 模型文件路径 | "/path/to/model.pt" |

### 可选配置

可以通过代码动态配置推理限制，无需额外的UI控件：

```python
# 动态配置推理限制
manager.set_inference_limit_config(enabled=True, default_limit=10)
```

## 数据库配置

### 数据库表结构

系统使用 `inference_statistics` 表存储推理统计数据：

```sql
CREATE TABLE inference_statistics (
    stat_id SERIAL PRIMARY KEY,
    image_id VARCHAR(500) NOT NULL,
    image_path VARCHAR(500) NOT NULL UNIQUE,
    inference_count INTEGER NOT NULL DEFAULT 0,
    model_md5 VARCHAR(32),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 数据库连接配置

默认数据库连接配置：
- Host: localhost
- Port: 5432
- User: postgres
- Password: 123456
- Database: 从 lineEdit_174 控件获取

## 集成到现有流程

### 1. 集成到PredictImageProcess

```python
class EnhancedPredictImageProcess(PredictImageProcess):
    def __init__(self, line_edit_manager, logger, log_output):
        super().__init__()
        self.inference_stats_manager = InferenceStatisticsManager(
            line_edit_manager=line_edit_manager,
            logger=logger,
            log_output=log_output
        )
    
    def __call__(self, shared_data_proxy, data_queue, process_logger, *args, **kwargs):
        images_to_predict = kwargs.get("images_to_predict", [])
        model_path = kwargs.get("model_path", None)
        
        # 过滤超过限制的图像
        filtered_results = self.inference_stats_manager.batch_check_inference_limits(images_to_predict)
        allowed_images = [path for path, allowed in filtered_results.items() if allowed]
        
        # 更新kwargs中的图像列表
        kwargs["images_to_predict"] = allowed_images
        
        # 执行原有的推理流程
        super().__call__(shared_data_proxy, data_queue, process_logger, *args, **kwargs)
        
        # 更新推理次数统计
        for image_path in allowed_images:
            self.inference_stats_manager.update_inference_count(image_path, model_path)
```

### 2. 集成到ImagePredictor

```python
class EnhancedImagePredictor(ImagePredictor):
    def __init__(self, line_edit_manager, label_manager, log_output, logger, checkbox_manager):
        super().__init__(line_edit_manager, label_manager, log_output, logger, checkbox_manager)
        
        self.inference_stats_manager = InferenceStatisticsManager(
            line_edit_manager=line_edit_manager,
            logger=logger,
            log_output=log_output
        )
    
    def predict_with_statistics(self, image_paths):
        # 推理前检查
        filtered = self.inference_stats_manager.batch_check_inference_limits(image_paths)
        allowed_images = filtered['allowed']
        blocked_images = filtered['blocked']
        
        if blocked_images:
            self.log_output.log_rich_text(
                f"[yellow]推理限制[/yellow]: {len(blocked_images)}张图像因达到限制被跳过"
            )
        
        # 执行推理
        for image_path in allowed_images:
            # 执行实际推理...
            # 更新统计
            self.inference_stats_manager.update_inference_count(image_path)
        
        # 生成统计报告
        self.inference_stats_manager.log_statistics_summary()
```

## API参考

### 核心方法

#### `check_inference_limit(image_path, limit=None)`
检查图像是否超过推理次数限制

**参数:**
- `image_path` (str): 图像文件路径
- `limit` (Optional[int]): 自定义限制值

**返回:** bool - 可以推理时返回True

#### `update_inference_count(image_path, model_path=None)`
更新图像的推理次数统计

**参数:**
- `image_path` (str): 图像文件路径
- `model_path` (Optional[str]): 模型文件路径

**返回:** bool - 更新成功时返回True

#### `get_inference_count(image_path)`
获取指定图像的推理次数

**参数:**
- `image_path` (str): 图像文件路径

**返回:** Optional[int] - 推理次数

#### `batch_check_inference_limits(image_paths, limit=None)`
批量检查多个图像的推理次数限制

**参数:**
- `image_paths` (List[str]): 图像文件路径列表
- `limit` (Optional[int]): 自定义限制值

**返回:** Dict[str, bool] - 图像路径到检查结果的映射

### 统计方法

#### `get_statistics_summary()`
获取推理统计摘要信息

**返回:** Optional[Dict[str, Any]] - 统计摘要字典

#### `get_top_inference_images(limit=10)`
获取推理次数最多的图像列表

**参数:**
- `limit` (int): 返回的记录数量限制

**返回:** Optional[List[Dict[str, Any]]] - 图像推理统计列表

#### `log_statistics_summary()`
记录统计摘要到日志和UI

### 配置方法

#### `set_inference_limit_config(enabled=True, default_limit=10)`
设置推理次数限制配置

**参数:**
- `enabled` (bool): 是否启用推理次数限制
- `default_limit` (int): 默认推理次数限制

#### `clear_cache()`
清除所有缓存数据

#### `get_config_info()`
获取当前配置信息

**返回:** Dict[str, Any] - 配置信息字典

## 错误处理

### 常见错误及解决方案

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接配置是否正确
   - 确认数据库名称配置（lineEdit_174）

2. **模型文件不存在**
   - 检查模型路径配置（lineEdit_63）
   - 验证文件路径是否正确

3. **表创建失败**
   - 检查数据库用户权限
   - 确认数据库连接正常

### 日志级别

系统提供详细的日志记录：
- DEBUG: 详细的操作信息
- INFO: 重要的状态变化
- WARNING: 潜在问题提醒
- ERROR: 错误信息

## 性能优化

### 缓存机制
- 模型MD5值缓存，避免重复计算
- 数据库名称缓存，减少UI控件访问

### 批量处理
- 支持批量推理限制检查
- 优化数据库连接管理

### 数据库优化
- 自动创建索引提高查询性能
- 使用参数化查询防止SQL注入

## 测试

运行测试文件验证功能：

```bash
cd anylabeling\customize_ui\src\ui_operate\predict_image
python test_inference_statistics.py
```

运行集成示例：

```bash
python integration_example.py
```

## 版本信息

- 版本: 1.0.0
- 创建时间: 2025-01-21
- 作者: AI Assistant
- 依赖: PostgreSQL, global_tools
